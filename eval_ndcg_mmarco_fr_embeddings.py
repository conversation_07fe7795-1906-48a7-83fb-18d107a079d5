import os

import numpy as np

from datasets import load_dataset, DatasetDict, Features, Value
from huggingface_hub import snapshot_download

from sentence_transformers import SentenceTransformer, util
from tqdm import tqdm


def dcg_at_k(relevances, k=10):
    relevances = np.asfarray(relevances)[:k]
    if relevances.size:
        return np.sum((2**relevances - 1) / np.log2(np.arange(2, relevances.size + 2)))
    return 0.0


def ndcg_at_k(relevances, k=10):
    dcg = dcg_at_k(relevances, k)
    ideal_dcg = dcg_at_k(sorted(relevances, reverse=True), k)
    return dcg / ideal_dcg if ideal_dcg > 0 else 0.0



def load_dataset_mmarco_fr():
    # Forcer l’usage de Xet
    os.environ["HF_HUB_ENABLE_XET"] = "1"
    # Activer le transfert optimisé (multi-thread)
    os.environ["HF_XET_HIGH_PERFORMANCE"] = "1"

    print("Chargement du dataset MMARCO (fr)...")

    # Télécharger uniquement les fichiers FR utiles : queries + collection
    local_dir = snapshot_download(
        repo_id="unicamp-dl/mmarco",
        repo_type="dataset",
        allow_patterns=[
            "data/google/queries/train/french_queries.train.tsv",
            "data/google/queries/dev/french_queries.dev.tsv",
            "data/google/collections/french_collection.tsv",
        ],
    )

    features_queries = Features(
        {
            "id": Value("int64"),
            "text": Value("string"),
        }
    )

    features_collection = Features(
        {
            "id": Value("int64"),
            "text": Value("string"),
        }
    )

    train = load_dataset(
        "csv",
        data_files=f"{local_dir}/data/google/queries/train/french_queries.train.tsv",
        delimiter="\t",
        column_names=["id", "text"],
        features=features_queries,
        split="train",
    )

    dev = load_dataset(
        "csv",
        data_files=f"{local_dir}/data/google/queries/dev/french_queries.dev.tsv",
        delimiter="\t",
        column_names=["id", "text"],
        features=features_queries,
        split="train",
    )

    collection = load_dataset(
        "csv",
        data_files=f"{local_dir}/data/google/collections/french_collection.tsv",
        delimiter="\t",
        column_names=["id", "text"],
        features=features_collection,
        split="train",
    )

    return DatasetDict(
        {"train_queries": train, "dev_queries": dev, "collection": collection}
    )  # type: ignore



def show_query_and_doc(ds, query_id, split="train_queries"):
    query_text = ds[f"{split}_queries"].filter(lambda x: x["id"] == query_id)[0]["text"]
    doc_text = ds["collection"].filter(lambda x: x["id"] == query_id)[0]["text"]

    print(f"Split : {split}")
    print(f"Query ID : {query_id}")
    print(f"Query : {query_text}")
    print(f"Passage : {doc_text[:200]}...")


if __name__ == "__main__":
    print("=== Évaluation nDCG@10 sur MMARCO (FR) ===")

    ds_fr = load_dataset_mmarco_fr()
    print(ds_fr)
    show_query_and_doc(ds_fr, 1, 1)


"""

# On prend un sous-ensemble (200 queries pour démo rapide)
subset = dataset["validation"].select(range(200))

# ---- Charger modèle embeddings ----
print("Chargement du modèle d'embeddings...")
# https://huggingface.co/intfloat/multilingual-e5-base
model = SentenceTransformer("intfloat/multilingual-e5-base")
# https://huggingface.co/google/mt5-large
# google/mt5-large
# https://huggingface.co/sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
# paraphrase-multilingual-mpnet-base-v2
#
# BAAI/bge-multilingual-gemma2
# https://huggingface.co/BAAI/bge-multilingual-gemma2


# ---- Évaluation nDCG@10 ----
scores = []
for sample in tqdm(subset, desc="Évaluation"):
    query = sample["query"]

    # Documents candidats = positifs + négatifs
    pos_docs = [p["passage_text"] for p in sample["positive_passages"]]
    neg_docs = [n["passage_text"] for n in sample["negative_passages"]]
    all_docs = pos_docs + neg_docs

    # Encoder query + docs
    q_emb = model.encode(query, convert_to_tensor=True)
    d_embs = model.encode(all_docs, convert_to_tensor=True)

    # Similarité cosinus
    scores_docs = util.cos_sim(q_emb, d_embs)[0].cpu().numpy()

    # Tri décroissant
    ranked_indices = np.argsort(-scores_docs)
    ranked_docs = [all_docs[i] for i in ranked_indices]

    # Pertinence (1 si positif, 0 sinon)
    res_relevances = [1 if d in pos_docs else 0 for d in ranked_docs]

    # nDCG@10
    scores.append(ndcg_at_k(res_relevances, k=10))

print(f"\nScore moyen nDCG@10 sur 200 queries FR (embeddings) : {np.mean(scores):.4f}")

"""
